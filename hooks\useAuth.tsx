import { useState, useEffect } from "react";
import { SignedIn, SignedOut, useClerk, useUser } from "@clerk/clerk-expo";
import { router } from "expo-router";
import { createHandleErrorDialog } from "@/lib/errors";
import { z } from "zod";
import { API_URLS, fetchApi } from "@/config/api";
import { TFunction } from "i18next";

export const myUserProfileSchema = (t: TFunction<"translation", undefined>) =>
  z.object({
    id: z.string({ message: t("profile.validation.invalid_id") }),
    name: z.string({ message: t("profile.validation.invalid_name") }),
    firstName: z
      .string()
      .min(2, t("profile.validation.first_name_min", { min: 2 }))
      .max(50, t("profile.validation.first_name_max", { max: 50 })),
    lastName: z
      .string()
      .min(2, t("profile.validation.last_name_min", { min: 2 }))
      .max(50, t("profile.validation.last_name_max", { max: 50 })),
    email: z.string().email(t("profile.validation.invalid_email")),
    emailVerified: z.boolean(),
    phoneNumber: z
      .string()
      .length(9, t("profile.validation.invalid_phone"))
      .optional(),
    website: z.string().url(t("profile.validation.invalid_website")).optional(),
    gender: z.enum(["male", "female", "prefer_not_say"]).optional(),
    birthday: z.coerce.date().optional(),
  });

export type MyUserProfileType = z.infer<ReturnType<typeof myUserProfileSchema>>;

const toMyUserProfile = (user: any): MyUserProfileType | null => {
  if (!user) return null;
  return {
    id: user.id,
    email: user.emailAddresses[0].emailAddress,
    emailVerified: user.emailAddresses[0].verification.status === "verified",
    name: user.fullName,
    firstName: user.firstName,
    lastName: user.lastName,
    phoneNumber: user.unsafeMetadata.phoneNumber,
    website: user.unsafeMetadata.website,
    gender: user.unsafeMetadata.gender,
    birthday: user.unsafeMetadata.birthday
      ? new Date(user.unsafeMetadata.birthday)
      : undefined,
  };
};

const useAuth = () => {
  const { user, isLoaded, isSignedIn } = useUser();
  const { signOut } = useClerk();

  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [_user, setUser] = useState<MyUserProfileType | null>(null);
  const [isLoadingUser, setIsLoadingUser] = useState(isLoaded);

  useEffect(() => {
    if (isLoaded) {
      setIsAuthenticated(isSignedIn);
      setUser(toMyUserProfile(user));
      setIsLoadingUser(false);
    }
  }, [isLoaded, isSignedIn, user]);

  const handleSignOut = () => {
    signOut()
      .catch(async (err) => {
        createHandleErrorDialog({
          title: "Erro ao terminar sessão",
          error: err,
        });
      })
      .then(() => router.replace("/sign-in"));
  };

  const deleteUser = async () => {
    try {
      setIsLoadingUser(true);
      if (!_user?.id) {
        throw new Error("User not found");
      }

      await fetchApi(API_URLS.users, {
        method: "DELETE",
        body: JSON.stringify({ userId: _user.id }),
      });

      await handleSignOut();
    } catch (err) {
      createHandleErrorDialog({
        title: "Erro ao excluir conta",
        message:
          "Ocorreu um erro ao excluir a sua conta. Tente novamente mais tarde.",
        error: err as Error,
      });
      throw err;
    } finally {
      setIsLoadingUser(false);
    }
  };

  return {
    user: _user,
    isSignedIn: isAuthenticated,
    isLoadingUser,
    signOut: handleSignOut,
    deleteUser,
    SignedOut,
    SignedIn,
    userClient: user,
  };
};

export default useAuth;
