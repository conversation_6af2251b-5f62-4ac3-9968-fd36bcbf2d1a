import { Ionicons } from "@expo/vector-icons";
import React, { useState } from "react";
import {
  StyleProp,
  Text,
  TextStyle,
  View,
  ViewStyle,
  StyleSheet,
} from "react-native";
// import globalStyles from "src/styles/global";
import globalStyles from "@/lib/globalStyles";
import { Dropdown } from "react-native-element-dropdown";
import { useTranslation } from "react-i18next";

type ItemType<Type> = Type extends {
  name?: string;
  value?: string;
}
  ? Type
  : never;

type Props<Type> = {
  items: ItemType<Type>[];
  selected?: ItemType<Type>;
  onSelectItem?: (item: ItemType<Type>) => void;
  label?: string;
  style?: StyleProp<ViewStyle>;
  textStyle?: StyleProp<TextStyle>;
  dropdownStyle?: StyleProp<ViewStyle>;
  listStyle?: StyleProp<ViewStyle>;
  theme?: "primary" | "secondary";
  size?: "sm" | "md";
  hasActiveColor?: boolean;
  withSearch?: boolean;
  defaultValue?: string;
};

const InputSelect = <Type,>({
  items,
  selected,
  label,
  onSelectItem,
  hasActiveColor = false,
  style,
  textStyle,
  theme = "primary",
  size = "sm",
  withSearch = false,
  defaultValue,
  dropdownStyle,
  listStyle,
}: Props<Type>) => {
  const { t } = useTranslation();
  const [isFocus, setIsFocus] = useState(false);

  const renderLabel = () => {
    if (selected || isFocus) {
      return <Text style={[styles.label]}>{label}</Text>;
    }
    return null;
  };

  const getThemeStyles = () => {
    if (hasActiveColor && !!selected) {
      return styles.activeTheme;
    }

    switch (theme) {
      case "secondary":
        return styles.secondaryTheme;
      default:
        return styles.primaryTheme;
    }
  };

  return (
    <View style={[styles.container, getThemeStyles(), style]}>
      {renderLabel()}
      <Dropdown
        style={[styles.dropdown, dropdownStyle]}
        placeholderStyle={[
          styles.textStyle,
          textStyle,
          hasActiveColor && { color: globalStyles.colors.primary1 },
        ]}
        containerStyle={listStyle}
        selectedTextStyle={[
          styles.textStyle,
          textStyle,
          selected?.value && { color: globalStyles.colors.primary1 },
        ]}
        inputSearchStyle={{
          height: 35,
          fontSize: globalStyles.size.sm,
        }}
        data={items}
        search={withSearch}
        maxHeight={300}
        labelField="name"
        valueField="value"
        itemTextStyle={{
          color: globalStyles.colors.dark.secondary,
        }}
        placeholder={label}
        searchPlaceholder={t("common.search")}
        value={selected?.value || defaultValue}
        onFocus={() => setIsFocus(true)}
        onBlur={() => setIsFocus(false)}
        onChange={(item) => {
          onSelectItem?.(item);
          setIsFocus(false);
        }}
        renderRightIcon={() => (
          <Ionicons
            color={globalStyles.colors.primary1}
            name="caret-down-sharp"
            size={15}
          />
        )}
      />
    </View>
  );
};

export default InputSelect;

const styles = StyleSheet.create({
  container: {
    justifyContent: "center",
    padding: 0,
    paddingHorizontal: globalStyles.gap.xs,
    paddingVertical: globalStyles.gap["2xs"],
    borderWidth: 1,
    borderColor: globalStyles.colors.primary1,
    borderRadius: globalStyles.rounded.full,
  },
  dropdown: {
    width: "100%",
    minWidth: 100,
  },
  label: {
    position: "absolute",
    top: -18,
    fontSize: globalStyles.size.sm,
    color: globalStyles.colors.tertiary2,
    paddingLeft: globalStyles.size.xs,
  },
  textStyle: {
    fontSize: globalStyles.size.lg,
    textAlign: "center",
    lineHeight: globalStyles.size.textAdjustLineHeight,
  },
  primaryTheme: {
    backgroundColor: "transparent",
  },
  secondaryTheme: {
    backgroundColor: "white",
  },
  activeTheme: {
    backgroundColor: globalStyles.colors.tertiary3,
    borderColor: globalStyles.colors.white,
  },
  dropdownContainer: {
    alignSelf: "flex-end",
  },
});
